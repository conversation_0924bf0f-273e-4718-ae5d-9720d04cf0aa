#include <windows.h>
#include <wininet.h>
#include <urlmon.h>
#include <iostream>
#include <string>
#pragma comment(lib, "urlmon.lib")

std::string getHTML(const std::string& url) {
    HINTERNET hInternet = InternetOpenA("MegaHackBypasser/1.0", INTERNET_OPEN_TYPE_DIRECT, NULL, NULL, 0);
    if (!hInternet) return "";
    HINTERNET hConnection = InternetOpenUrlA(hInternet, url.c_str(), NULL, 0, INTERNET_FLAG_RELOAD, 0);
    if (!hConnection) {
        InternetCloseHandle(hInternet);
        return "";
    }

    std::string htmlContent;
    char buffer[4096];
    DWORD bytesRead;
    while (InternetReadFile(hConnection, buffer, sizeof(buffer), &bytesRead) && bytesRead > 0) {
        htmlContent.append(buffer, bytesRead);
    }

    InternetCloseHandle(hConnection);
    InternetCloseHandle(hInternet);
    return htmlContent;
}

std::string findDownloadURL(const std::string& html) {
    size_t pos = html.find("downloads/mega-hack");
    if (pos == std::string::npos) return "";
    size_t hrefStart = html.rfind("href=\"", pos);
    if (hrefStart == std::string::npos) return "";
    hrefStart += 6;
    size_t hrefEnd = html.find("\"", hrefStart);
    if (hrefEnd == std::string::npos) return "";
    return "https://absolllute.com" + html.substr(hrefStart, hrefEnd - hrefStart);
}

int main() {
    std::string targetURL = "https://absolllute.com/store/mega_hack";

    std::cout << "[+] Fetching page..." << std::endl;
    std::string html = getHTML(targetURL);
    if (html.empty()) {
        std::cerr << "Failed to get HTML." << std::endl;
        return 1;
    }

    std::cout << "[+] Finding download..." << std::endl;
    std::string downloadUrl = findDownloadURL(html);
    if (downloadUrl.empty()) {
        std::cerr << "Could not find download link." << std::endl;
        return 1;
    }
    std::cout << "[+] Found: " << downloadUrl << std::endl;

    std::string savePath = "C:\\Windows\\Temp\\MegaHack.exe";

    std::cout << "[+] Downloading..." << std::endl;
    if (URLDownloadToFileA(NULL, downloadUrl.c_str(), savePath.c_str(), 0, NULL) != S_OK) {
        std::cerr << "Download failed!" << std::endl;
        return 1;
    }
    std::cout << "[+] Download complete: " << savePath << std::endl;

    std::cout << "[+] The installer has been downloaded. For the bypass, tell them to just click 'Skip' or enter anything. The patch is applied server-side by the downloader." << std::endl;
    ShellExecuteA(NULL, "open", savePath.c_str(), NULL, NULL, SW_SHOWNORMAL);

    return 0;
}